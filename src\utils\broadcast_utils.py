"""
Broadcast utility functions for error handling and user management
"""

import logging
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timezone
from telegram.error import TelegramError, Forbidden, BadRequest, TimedOut, NetworkError

logger = logging.getLogger(__name__)

class BroadcastErrorHandler:
    """Comprehensive error handler for broadcast operations"""
    
    @staticmethod
    def categorize_telegram_error(error: Exception) -> Dict[str, str]:
        """Categorize Telegram API errors for proper handling"""
        error_msg = str(error).lower()
        
        if isinstance(error, Forbidden):
            if any(phrase in error_msg for phrase in [
                "bot was blocked", 
                "user is deactivated", 
                "blocked by the user",
                "bot was kicked"
            ]):
                return {
                    'type': 'blocked',
                    'category': 'user_blocked',
                    'cleanup_required': True,
                    'retry_recommended': False,
                    'message': str(error)
                }
            elif "user not found" in error_msg:
                return {
                    'type': 'deleted',
                    'category': 'user_deleted',
                    'cleanup_required': True,
                    'retry_recommended': False,
                    'message': str(error)
                }
            else:
                return {
                    'type': 'forbidden',
                    'category': 'permission_denied',
                    'cleanup_required': False,
                    'retry_recommended': False,
                    'message': str(error)
                }
        
        elif isinstance(error, BadRequest):
            if any(phrase in error_msg for phrase in [
                "user not found",
                "chat not found",
                "user_id_invalid"
            ]):
                return {
                    'type': 'deleted',
                    'category': 'user_deleted',
                    'cleanup_required': True,
                    'retry_recommended': False,
                    'message': str(error)
                }
            elif "message is too long" in error_msg:
                return {
                    'type': 'content_error',
                    'category': 'message_too_long',
                    'cleanup_required': False,
                    'retry_recommended': False,
                    'message': str(error)
                }
            else:
                return {
                    'type': 'bad_request',
                    'category': 'invalid_request',
                    'cleanup_required': False,
                    'retry_recommended': False,
                    'message': str(error)
                }
        
        elif isinstance(error, (TimedOut, NetworkError)):
            return {
                'type': 'network',
                'category': 'network_error',
                'cleanup_required': False,
                'retry_recommended': True,
                'message': str(error)
            }
        
        else:
            return {
                'type': 'unknown',
                'category': 'unknown_error',
                'cleanup_required': False,
                'retry_recommended': True,
                'message': str(error)
            }
    
    @staticmethod
    def should_retry_error(error_info: Dict[str, str]) -> bool:
        """Determine if an error should be retried"""
        return error_info.get('retry_recommended', False)
    
    @staticmethod
    def requires_user_cleanup(error_info: Dict[str, str]) -> bool:
        """Determine if user should be removed from database"""
        return error_info.get('cleanup_required', False)

class BroadcastUserManager:
    """Manages user cleanup and validation for broadcasts"""
    
    def __init__(self, database):
        self.database = database
        self.users_collection = database.db.users
    
    async def validate_user_exists(self, user_id: int) -> bool:
        """Check if user exists in database"""
        try:
            user = await self.users_collection.find_one({"user_id": user_id})
            return user is not None
        except Exception as e:
            logger.error(f"Error validating user {user_id}: {e}")
            return False
    
    async def cleanup_invalid_users(self, user_ids: List[int]) -> Tuple[int, List[int]]:
        """Remove invalid users from database and return cleanup statistics"""
        try:
            if not user_ids:
                return 0, []
            
            # Remove users from database
            result = await self.users_collection.delete_many({
                "user_id": {"$in": user_ids}
            })
            
            deleted_count = result.deleted_count
            
            # Also cleanup related data
            await self._cleanup_related_data(user_ids)
            
            logger.info(f"Cleaned up {deleted_count} invalid users from database")
            return deleted_count, user_ids
            
        except Exception as e:
            logger.error(f"Error cleaning up users: {e}")
            return 0, []
    
    async def _cleanup_related_data(self, user_ids: List[int]):
        """Clean up related user data from other collections"""
        try:
            # Clean up referrals
            await self.database.db.referrals.delete_many({
                "$or": [
                    {"referrer_user_id": {"$in": user_ids}},
                    {"referred_user_id": {"$in": user_ids}}
                ]
            })
            
            # Clean up transactions
            await self.database.db.transactions.delete_many({
                "user_id": {"$in": user_ids}
            })
            
            logger.debug(f"Cleaned up related data for {len(user_ids)} users")
            
        except Exception as e:
            logger.error(f"Error cleaning up related data: {e}")
    
    async def mark_users_inactive(self, user_ids: List[int]) -> int:
        """Mark users as inactive instead of deleting them"""
        try:
            if not user_ids:
                return 0
            
            result = await self.users_collection.update_many(
                {"user_id": {"$in": user_ids}},
                {
                    "$set": {
                        "is_active": False,
                        "deactivated_at": datetime.now(timezone.utc).isoformat(),
                        "deactivation_reason": "broadcast_delivery_failed"
                    }
                }
            )
            
            modified_count = result.modified_count
            logger.info(f"Marked {modified_count} users as inactive")
            return modified_count
            
        except Exception as e:
            logger.error(f"Error marking users inactive: {e}")
            return 0
    
    async def get_user_statistics(self) -> Dict[str, int]:
        """Get user statistics for monitoring"""
        try:
            total_users = await self.users_collection.count_documents({})
            active_users = await self.users_collection.count_documents({"is_active": True})
            inactive_users = await self.users_collection.count_documents({"is_active": False})
            banned_users = await self.users_collection.count_documents({"is_banned": True})
            
            return {
                "total_users": total_users,
                "active_users": active_users,
                "inactive_users": inactive_users,
                "banned_users": banned_users
            }
            
        except Exception as e:
            logger.error(f"Error getting user statistics: {e}")
            return {}

class BroadcastProgressTracker:
    """Tracks and reports broadcast progress"""
    
    def __init__(self, broadcast_id: str, total_users: int):
        self.broadcast_id = broadcast_id
        self.total_users = total_users
        self.start_time = datetime.now(timezone.utc)
        self.last_update = self.start_time
        
        self.stats = {
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'blocked': 0,
            'deleted': 0,
            'network_errors': 0,
            'other_errors': 0
        }
    
    def update_stats(self, **kwargs):
        """Update statistics"""
        for key, value in kwargs.items():
            if key in self.stats:
                self.stats[key] += value
        
        self.last_update = datetime.now(timezone.utc)
    
    def get_progress_percentage(self) -> float:
        """Get completion percentage"""
        if self.total_users == 0:
            return 100.0
        return (self.stats['processed'] / self.total_users) * 100
    
    def get_success_rate(self) -> float:
        """Get success rate percentage"""
        if self.stats['processed'] == 0:
            return 0.0
        return (self.stats['successful'] / self.stats['processed']) * 100
    
    def get_estimated_completion(self) -> Optional[datetime]:
        """Estimate completion time"""
        if self.stats['processed'] == 0:
            return None
        
        elapsed = (self.last_update - self.start_time).total_seconds()
        rate = self.stats['processed'] / elapsed  # users per second
        
        remaining_users = self.total_users - self.stats['processed']
        if rate > 0:
            remaining_seconds = remaining_users / rate
            return self.last_update + timedelta(seconds=remaining_seconds)
        
        return None
    
    def get_summary(self) -> Dict[str, any]:
        """Get comprehensive progress summary"""
        elapsed = (self.last_update - self.start_time).total_seconds()
        
        return {
            'broadcast_id': self.broadcast_id,
            'total_users': self.total_users,
            'progress_percentage': self.get_progress_percentage(),
            'success_rate': self.get_success_rate(),
            'elapsed_seconds': elapsed,
            'estimated_completion': self.get_estimated_completion(),
            'stats': self.stats.copy(),
            'last_update': self.last_update.isoformat()
        }

def format_broadcast_duration(seconds: float) -> str:
    """Format duration in human-readable format"""
    if seconds < 60:
        return f"{seconds:.0f} seconds"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f} minutes"
    else:
        hours = seconds / 3600
        return f"{hours:.1f} hours"

def estimate_broadcast_time(user_count: int, rate_limit: int) -> str:
    """Estimate broadcast completion time"""
    if user_count == 0:
        return "0 seconds"
    
    seconds = user_count / rate_limit
    return format_broadcast_duration(seconds)

def validate_broadcast_content(text_content: Optional[str], media_content: Optional[Dict]) -> Tuple[bool, List[str]]:
    """Validate broadcast content"""
    errors = []
    
    # Check if content exists
    if not text_content and not media_content:
        errors.append("No content provided")
        return False, errors
    
    # Validate text content
    if text_content:
        if len(text_content) > 4096:
            errors.append("Text message too long (max 4096 characters)")
        
        if len(text_content.strip()) == 0:
            errors.append("Text message cannot be empty")
    
    # Validate media content
    if media_content:
        if not media_content.get('file_id'):
            errors.append("Media file ID is required")
        
        caption = media_content.get('caption', '')
        if caption and len(caption) > 1024:
            errors.append("Media caption too long (max 1024 characters)")
    
    return len(errors) == 0, errors
