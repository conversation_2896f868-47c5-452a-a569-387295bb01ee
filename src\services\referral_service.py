"""
Referral service for managing referral operations.

REFERRAL FLOW DOCUMENTATION:
===========================

1. USER REGISTRATION WITH REFERRAL:
   - User clicks referral link: https://t.me/bot?start=REFERRAL_CODE
   - <PERSON><PERSON> extracts referral code from /start command
   - User data is stored with referrer relationship
   - Friend welcome bonus is applied immediately

2. REFERRAL CREATION:
   - Referral record is created in PENDING status
   - Referral waits for channel membership verification
   - No rewards are distributed until validation

3. CHANNEL VERIFICATION:
   - User must join required channels to activate referral
   - <PERSON><PERSON> verifies membership using Telegram API
   - Only verified members can earn/receive referral rewards

4. REFERRAL COMPLETION (ATOMIC):
   - Referral status changes from PENDING to COMPLETED
   - Referrer receives reward (50 Genesis Tokens)
   - Transaction record is created for audit trail
   - User statistics are updated (successful_referrals++)
   - All operations happen in single MongoDB transaction

5. TRANSACTION LIFECYCLE:
   - Every balance change creates a Transaction record
   - Transactions include: type, amount, status, description, reference_id
   - Provides complete audit trail for all financial operations
   - Supports transaction history and reporting

SECURITY FEATURES:
==================
- Atomic transactions prevent partial state updates
- Referral validation prevents self-referrals
- Channel membership verification prevents abuse
- Comprehensive logging for audit and debugging
- Input validation and error handling throughout
"""

import logging
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError, OperationFailure
import asyncio

from src.database import Database
from src.models.referral import Referral, ReferralStatus
from src.models.transaction import Transaction, TransactionType, TransactionStatus
from src.utils.logger import log_transaction
from config import Config

logger = logging.getLogger(__name__)

def retry_on_connection_error(max_retries: int = 3, delay: float = 1.0):
    """Decorator to retry database operations on connection errors"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except (ConnectionFailure, ServerSelectionTimeoutError) as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        logger.warning(f"Database connection error on attempt {attempt + 1}, retrying in {delay}s: {e}")
                        await asyncio.sleep(delay * (attempt + 1))  # Exponential backoff
                    else:
                        logger.error(f"Database connection failed after {max_retries} attempts: {e}")
                        raise
                except Exception as e:
                    # Don't retry on other types of exceptions
                    raise
            raise last_exception
        return wrapper
    return decorator

class ReferralService:
    """Service for referral management operations"""

    def __init__(self, database: Database):
        self.db = database
    
    async def create_referral(self, referrer_id: int, referred_id: int, referral_code: str, validate_immediately: bool = False) -> Optional[Referral]:
        """Create a new referral with optional immediate validation"""
        try:
            # Check if referral already exists
            existing = await self.db.referrals.find_one({
                'referrer_id': referrer_id,
                'referred_id': referred_id
            })

            if existing:
                return Referral.from_dict(existing)

            # Use static referral reward amount from config
            reward_amount = Config.REFERRAL_REWARD

            # Create referral - starts as PENDING by default
            referral = Referral(
                referrer_id=referrer_id,
                referred_id=referred_id,
                referral_code=referral_code,
                reward_amount=reward_amount,
                status=ReferralStatus.PENDING  # Always start as pending
            )

            # Save to database
            await self.db.referrals.insert_one(referral.to_dict())
            logger.info(f"Created pending referral: {referrer_id} -> {referred_id}")

            # If immediate validation is requested (for channel members), complete it
            if validate_immediately:
                await self.complete_referral(referral.referral_id)
                logger.info(f"Immediately validated referral: {referrer_id} -> {referred_id}")

            return referral

        except Exception as e:
            logger.error(f"Failed to create referral {referrer_id} -> {referred_id}: {e}")
            return None

    async def create_pending_referral(self, referrer_id: int, referred_id: int, referral_code: str) -> Optional[Referral]:
        """Create a pending referral that requires channel validation"""
        return await self.create_referral(referrer_id, referred_id, referral_code, validate_immediately=False)

    async def validate_pending_referrals(self, user_id: int) -> List[str]:
        """Validate all pending referrals for a user who just joined the channel"""
        try:
            # Find all pending referrals where this user was referred
            pending_referrals = await self.db.referrals.find({
                'referred_id': user_id,
                'status': ReferralStatus.PENDING.value
            }).to_list(None)

            validated_referral_ids = []

            for referral_data in pending_referrals:
                referral = Referral.from_dict(referral_data)

                # Complete the referral
                success = await self.complete_referral(referral.referral_id)
                if success:
                    validated_referral_ids.append(referral.referral_id)
                    logger.info(f"✅ Validated pending referral: {referral.referrer_id} -> {user_id}")
                else:
                    logger.error(f"❌ Failed to validate pending referral: {referral.referrer_id} -> {user_id}")

            return validated_referral_ids

        except Exception as e:
            logger.error(f"Failed to validate pending referrals for user {user_id}: {e}")
            return []

    async def get_pending_referrals_count(self, user_id: int) -> int:
        """Get count of pending referrals for a user"""
        try:
            count = await self.db.referrals.count_documents({
                'referrer_id': user_id,
                'status': ReferralStatus.PENDING.value
            })
            return count
        except Exception as e:
            logger.error(f"Failed to get pending referrals count for user {user_id}: {e}")
            return 0

    @retry_on_connection_error(max_retries=3)
    async def get_referred_users_paginated(self, referrer_id: int, page: int = 0, per_page: int = 10) -> List[Dict[str, Any]]:
        """
        Get paginated list of users referred by a specific user.

        Args:
            referrer_id (int): ID of the user who made the referrals
            page (int): Page number (0-based)
            per_page (int): Number of referrals per page (max 20)

        Returns:
            List[Dict[str, Any]]: List of referred user information with details

        Raises:
            ValueError: If parameters are invalid
            OperationFailure: If database operation fails

        Note:
            - Returns user details including name, join date, and status
            - Includes both completed and pending referrals
            - Sorted by creation date (newest first)
        """
        try:
            # Validate parameters
            if not isinstance(referrer_id, int) or referrer_id <= 0:
                raise ValueError(f"Invalid referrer_id: {referrer_id}")
            if not isinstance(page, int) or page < 0:
                raise ValueError(f"Invalid page number: {page}")
            if not isinstance(per_page, int) or per_page < 1 or per_page > 20:
                raise ValueError(f"Invalid per_page value: {per_page} (must be 1-20)")

            # Calculate skip value
            skip = page * per_page

            # Get referrals with user details using aggregation pipeline
            pipeline = [
                # Match referrals by referrer
                {"$match": {"referrer_id": referrer_id}},

                # Sort by creation date (newest first)
                {"$sort": {"created_at": -1}},

                # Pagination
                {"$skip": skip},
                {"$limit": per_page},

                # Lookup user details
                {
                    "$lookup": {
                        "from": "users",
                        "localField": "referred_user_id",
                        "foreignField": "user_id",
                        "as": "user_details"
                    }
                },

                # Unwind user details
                {"$unwind": {"path": "$user_details", "preserveNullAndEmptyArrays": True}},

                # Project required fields
                {
                    "$project": {
                        "referral_id": 1,
                        "status": 1,
                        "created_at": 1,
                        "completed_at": 1,
                        "user_name": {"$ifNull": ["$user_details.first_name", "Unknown User"]},
                        "user_username": {"$ifNull": ["$user_details.username", ""]},
                        "user_active": {"$ifNull": ["$user_details.is_active", False]},
                        "user_banned": {"$ifNull": ["$user_details.is_banned", True]},
                        "user_balance": {"$ifNull": ["$user_details.balance", 0]}
                    }
                }
            ]

            cursor = self.db.db.referrals.aggregate(pipeline)
            referred_users = []

            async for referral_data in cursor:
                referred_users.append(referral_data)

            logger.debug(f"Retrieved {len(referred_users)} referred users for user {referrer_id}, page {page}")
            return referred_users

        except ValueError as e:
            logger.error(f"Invalid parameters for referred users pagination: {e}")
            return []
        except OperationFailure as e:
            logger.error(f"Database operation failed for referred users: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error getting referred users for {referrer_id}: {e}")
            return []

    async def get_referred_users_total_pages(self, referrer_id: int, per_page: int = 10) -> int:
        """
        Get total number of pages for referred users pagination.

        Args:
            referrer_id (int): ID of the user who made the referrals
            per_page (int): Number of referrals per page

        Returns:
            int: Total number of pages
        """
        try:
            if not isinstance(referrer_id, int) or referrer_id <= 0:
                return 0

            total_count = await self.db.db.referrals.count_documents({"referrer_id": referrer_id})
            return (total_count + per_page - 1) // per_page  # Ceiling division

        except Exception as e:
            logger.error(f"Failed to get referred users total pages for {referrer_id}: {e}")
            return 0
    
    @retry_on_connection_error(max_retries=3)
    async def complete_referral(self, referral_id: str) -> bool:
        """
        Complete a referral and process reward with atomic transaction.

        This method handles the complete referral lifecycle:
        1. Validates referral exists and is in PENDING status
        2. Creates reward transaction for referrer
        3. Updates referrer's balance and statistics
        4. Marks referral as completed

        Args:
            referral_id (str): Unique referral identifier

        Returns:
            bool: True if referral completed successfully, False otherwise

        Raises:
            ValueError: If referral_id is invalid
            OperationFailure: If database operation fails
            ConnectionFailure: If database connection fails (auto-retried)

        Note:
            - Uses MongoDB transactions for atomic operations
            - Automatically rolls back on any failure
            - Includes comprehensive logging for audit trail
            - Only processes PENDING referrals
        """
        # Input validation
        if not referral_id or not isinstance(referral_id, str):
            logger.error(f"Invalid referral_id: {referral_id}")
            return False

        async with await self.db.client.start_session() as session:
            try:
                async with session.start_transaction():
                    # Get referral within transaction
                    referral_data = await self.db.referrals.find_one(
                        {'referral_id': referral_id},
                        session=session
                    )
                    if not referral_data:
                        logger.warning(f"Referral not found: {referral_id}")
                        await session.abort_transaction()
                        return False

                    referral = Referral.from_dict(referral_data)

                    if referral.status != ReferralStatus.PENDING:
                        logger.warning(f"Referral {referral_id} is not pending (status: {referral.status})")
                        await session.abort_transaction()
                        return False

                    # Create transaction for reward
                    transaction = Transaction(
                        user_id=referral.referrer_id,
                        amount=referral.reward_amount,
                        transaction_type=TransactionType.REFERRAL_BONUS,
                        status=TransactionStatus.COMPLETED,
                        description=f"Referral bonus: {referral.reward_amount} Genesis Tokens for user {referral.referred_id}",
                        reference_id=referral.referral_id
                    )

                    # All database operations within the same transaction
                    # 1. Save transaction
                    await self.db.transactions.insert_one(transaction.to_dict(), session=session)

                    # 2. Update user balance and referral count
                    user_update_result = await self.db.users.update_one(
                        {'user_id': referral.referrer_id},
                        {
                            '$inc': {
                                'balance': referral.reward_amount,
                                'total_earned': referral.reward_amount,
                                'successful_referrals': 1,
                                'referral_count': 1  # Add this to fix admin panel queries
                            },
                            '$set': {'updated_at': datetime.now(timezone.utc).isoformat()}
                        },
                        session=session
                    )

                    if user_update_result.modified_count == 0:
                        logger.error(f"Failed to update user balance for referral {referral_id}")
                        await session.abort_transaction()
                        return False

                    # 3. Complete referral
                    referral.complete_referral(referral.reward_amount, transaction.transaction_id)
                    referral.mark_rewarded(transaction.transaction_id)

                    # 4. Update referral in database
                    referral_update_result = await self.db.referrals.update_one(
                        {'referral_id': referral_id},
                        {'$set': referral.to_dict()},
                        session=session
                    )

                    if referral_update_result.modified_count == 0:
                        logger.error(f"Failed to update referral status for {referral_id}")
                        await session.abort_transaction()
                        return False

                    # Commit transaction
                    await session.commit_transaction()

                    # Log successful transaction (outside of DB transaction)
                    log_transaction(
                        referral.referrer_id,
                        "REFERRAL_BONUS",
                        referral.reward_amount,
                        "COMPLETED",
                        f"Referral ID: {referral_id}"
                    )

                    logger.info(f"✅ Atomically completed referral {referral_id}, rewarded {referral.reward_amount} Genesis Tokens")
                    return True

            except ValueError as e:
                logger.error(f"Validation error completing referral {referral_id}: {e}")
                try:
                    await session.abort_transaction()
                except:
                    pass
                return False
            except OperationFailure as e:
                logger.error(f"Database operation failed for referral {referral_id}: {e}")
                try:
                    await session.abort_transaction()
                except:
                    pass
                return False
            except Exception as e:
                logger.error(f"Unexpected error completing referral {referral_id}: {e}")
                try:
                    await session.abort_transaction()
                except:
                    pass  # Transaction may already be aborted
                return False
    
    async def get_user_referrals(self, user_id: int, limit: int = 50) -> List[Referral]:
        """Get referrals made by a user"""
        try:
            cursor = self.db.referrals.find({'referrer_id': user_id}).sort('created_at', -1).limit(limit)
            referrals = []
            
            async for referral_data in cursor:
                referrals.append(Referral.from_dict(referral_data))
            
            return referrals
            
        except Exception as e:
            logger.error(f"Failed to get referrals for user {user_id}: {e}")
            return []
    
    async def get_referral_count(self, user_id: int) -> int:
        """Get total referral count for a user"""
        try:
            return await self.db.referrals.count_documents({'referrer_id': user_id})
        except Exception as e:
            logger.error(f"Failed to get referral count for user {user_id}: {e}")
            return 0
    
    async def get_successful_referral_count(self, user_id: int) -> int:
        """Get successful referral count for a user"""
        try:
            return await self.db.referrals.count_documents({
                'referrer_id': user_id,
                'status': ReferralStatus.COMPLETED.value
            })
        except Exception as e:
            logger.error(f"Failed to get successful referral count for user {user_id}: {e}")
            return 0
    
    async def get_referral_earnings(self, user_id: int) -> float:
        """Get total earnings from referrals"""
        try:
            pipeline = [
                {'$match': {'referrer_id': user_id, 'is_rewarded': True}},
                {'$group': {'_id': None, 'total': {'$sum': '$reward_amount'}}}
            ]
            
            result = await self.db.referrals.aggregate(pipeline).to_list(1)
            
            if result:
                return result[0]['total']
            return 0.0
            
        except Exception as e:
            logger.error(f"Failed to get referral earnings for user {user_id}: {e}")
            return 0.0
    
    async def get_referral_leaderboard(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get referral leaderboard"""
        try:
            pipeline = [
                {'$match': {'status': ReferralStatus.COMPLETED.value}},
                {'$group': {
                    '_id': '$referrer_id',
                    'referral_count': {'$sum': 1},
                    'total_earned': {'$sum': '$reward_amount'}
                }},
                {'$sort': {'referral_count': -1}},
                {'$limit': limit}
            ]
            
            leaderboard = []
            async for entry in self.db.referrals.aggregate(pipeline):
                # Get user info
                user_data = await self.db.users.find_one({'user_id': entry['_id']})
                if user_data:
                    leaderboard.append({
                        'user_id': entry['_id'],
                        'username': user_data.get('username'),
                        'first_name': user_data.get('first_name'),
                        'referral_count': entry['referral_count'],
                        'total_earned': entry['total_earned']
                    })
            
            return leaderboard
            
        except Exception as e:
            logger.error(f"Failed to get referral leaderboard: {e}")
            return []
    
    async def get_referral_statistics(self) -> Dict[str, Any]:
        """Get overall referral statistics"""
        try:
            total_referrals = await self.db.referrals.count_documents({})
            completed_referrals = await self.db.referrals.count_documents({
                'status': ReferralStatus.COMPLETED.value
            })
            
            # Get total rewards paid
            pipeline = [
                {'$match': {'is_rewarded': True}},
                {'$group': {'_id': None, 'total': {'$sum': '$reward_amount'}}}
            ]
            
            result = await self.db.referrals.aggregate(pipeline).to_list(1)
            total_rewards = result[0]['total'] if result else 0.0
            
            # Get top referrer
            top_referrer_pipeline = [
                {'$match': {'status': ReferralStatus.COMPLETED.value}},
                {'$group': {
                    '_id': '$referrer_id',
                    'count': {'$sum': 1}
                }},
                {'$sort': {'count': -1}},
                {'$limit': 1}
            ]
            
            top_referrer_result = await self.db.referrals.aggregate(top_referrer_pipeline).to_list(1)
            top_referrer_id = top_referrer_result[0]['_id'] if top_referrer_result else None
            top_referrer_count = top_referrer_result[0]['count'] if top_referrer_result else 0
            
            return {
                'total_referrals': total_referrals,
                'completed_referrals': completed_referrals,
                'completion_rate': (completed_referrals / total_referrals * 100) if total_referrals > 0 else 0,
                'total_rewards_paid': total_rewards,
                'top_referrer_id': top_referrer_id,
                'top_referrer_count': top_referrer_count
            }
            
        except Exception as e:
            logger.error(f"Failed to get referral statistics: {e}")
            return {}
    
    async def invalidate_referral(self, referral_id: str, reason: str = "") -> bool:
        """Invalidate a referral"""
        try:
            referral_data = await self.db.referrals.find_one({'referral_id': referral_id})
            if not referral_data:
                return False
            
            referral = Referral.from_dict(referral_data)
            referral.invalidate_referral(reason)
            
            # Update in database
            await self.db.referrals.update_one(
                {'referral_id': referral_id},
                {'$set': referral.to_dict()}
            )
            return True
            
        except Exception as e:
            logger.error(f"Failed to invalidate referral {referral_id}: {e}")
            return False
